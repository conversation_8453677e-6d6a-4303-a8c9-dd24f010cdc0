import { createRouter, createWebHistory } from "vue-router";
import { getPageTitle, setPageTitle } from "@/utils/env";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: "home",
      path: "/",
      component: () => import("../views/404.vue"),
      meta: {
        title: "404 - Nustar",
      },
    },
    {
      name: "cocosPayResult",
      path: "/cocos/payResult",
      component: () => import("../views/PayResult/index.vue"),
      meta: {
        title: "Nustar Pay Result",
      },
    },
    {
      name: "vueH5PayResult",
      path: "/vueH5/payResult",
      component: () => import("../views/PayResult/index.vue"),
      meta: {
        title: "Nustar Pay Result",
      },
    },
    {
      name: "download",
      path: "/download",
      component: () => import("../views/DownloadPage/index.vue"),
      meta: {
        title: "Nustar Download",
      },
    },
    {
      name: "notSupported",
      path: "/notSupported",
      component: () => import("../views/DownloadPage/notSupported.vue"),
      meta: {
        title: "Nustar Download",
      },
    },
  ],
});

// 路由守卫：根据路由配置动态设置页面标题
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    setPageTitle(to.meta.title as string);
  } else {
    // 如果路由没有配置标题，根据路由名称获取标题
    const title = getPageTitle(to.name as string);
    setPageTitle(title);
  }
  next();
});

export default router;
