/**
 * 环境配置工具
 * 统一管理所有环境变量的访问和类型转换
 *
 * @description
 * 所有环境变量都通过此文件进行访问，确保类型安全和默认值处理
 * 环境配置文件位于 env/ 文件夹下
 */

export type AppEnv = "development" | "test" | "staging" | "production";

/**
 * 环境配置接口
 */
export interface EnvConfig {
  // 应用基础信息
  env: AppEnv;
  title: string;
  port: number;

  // API 相关配置
  baseURL: string;
  apiURL: string;
  apiTimeout: number;
  proxyTarget: string;

  // 资源相关配置
  assetsUrl: string;
  webURL: string;
  jumpURL: string;
  jumpVueURL: string;

  // 功能开关
  enableMock: boolean;
  enableDevtools: boolean;
  enableConsole: boolean;
  enableVConsole: boolean;

  // 构建相关配置
  dropConsole: boolean;
  buildCompress: string;
  buildCompressDeleteOriginFile: boolean;

  // 代理配置
  proxy: Array<[string, string]>;

  // 环境判断
  isDev: boolean;
  isTest: boolean;
  isStaging: boolean;
  isProd: boolean;
}

/**
 * 获取当前环境
 */
export const getAppEnv = (): AppEnv => {
  return (import.meta.env.VITE_APP_ENV as AppEnv) || "development";
};

/**
 * 判断是否为开发环境
 */
export const isDevelopment = (): boolean => {
  return getAppEnv() === "development";
};

/**
 * 判断是否为测试环境
 */
export const isTest = (): boolean => {
  return getAppEnv() === "test";
};

/**
 * 判断是否为预发环境
 */
export const isStaging = (): boolean => {
  return getAppEnv() === "staging";
};

/**
 * 判断是否为生产环境
 */
export const isProduction = (): boolean => {
  return getAppEnv() === "production";
};

// ==================== 基础环境信息 ====================

/**
 * 获取应用标题
 */
export const getAppTitle = (): string => {
  return import.meta.env.VITE_APP_TITLE || "Nustar Pay Result";
};

/**
 * 根据路由名称获取页面标题
 */
export const getPageTitle = (routeName?: string): string => {
  const titleMap: Record<string, string> = {
    home: "Nustar Pay Result",
    cocosPayResult: "Nustar Pay Result",
    vueH5PayResult: "Nustar Pay Result",
    download: "Nustar Download",
    notSupported: "Nustar Download",
    titleDemo: "Title Demo - Nustar",
  };

  return routeName && titleMap[routeName] ? titleMap[routeName] : getAppTitle();
};

/**
 * 设置页面标题
 */
export const setPageTitle = (title: string): void => {
  document.title = title;
};

// ==================== API 相关配置 ====================

/**
 * 获取 API 基础 URL
 */
export const getBaseURL = (): string => {
  return import.meta.env.VITE_BASE_URL || "https://dev.nustaronline.vip";
};

/**
 * 获取 API URL
 */
export const getApiURL = (): string => {
  return import.meta.env.VITE_API_URL || getBaseURL();
};

/**
 * 获取 API 超时时间
 */
export const getApiTimeout = (): number => {
  return Number(import.meta.env.VITE_API_TIMEOUT) || 30000;
};

// ==================== 资源相关配置 ====================

/**
 * 获取跳转 URL
 */
export const getJumpURL = (isVue: boolean): string => {
  const vueUrl = import.meta.env.VITE_JUMP_URL_VUE;
  const defaultUrl = import.meta.env.VITE_JUMP_URL;

  if (isVue) {
    const result = vueUrl || "";
    if (!result) {
      console.warn("VITE_JUMP_URL_VUE is not configured or empty");
    }
    return result;
  }

  const result = defaultUrl || "";
  if (!result) {
    console.warn("VITE_JUMP_URL is not configured or empty");
  }
  return result;
};

// ==================== 功能开关 ====================

/**
 * 是否启用控制台日志
 */
export const isConsoleEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_CONSOLE === "true";
};

/**
 * 检查是否为本地开发环境
 */
export const isLocalDev = (): boolean => {
  return (
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1" ||
    window.location.hostname.includes("192.168.")
  );
};

// ==================== 构建相关配置 ====================

// ==================== 代理配置 ====================

// ==================== 环境配置对象 ====================

/**
 * 完整的环境配置对象
 * 包含所有环境变量的访问器，符合 EnvConfig 接口
 */
export const envConfig: EnvConfig = {
  // 应用基础信息
  env: getAppEnv(),
  title: getAppTitle(),
  port: Number(import.meta.env.VITE_PORT) || 80,

  // API 相关配置
  baseURL: getBaseURL(),
  apiURL: getApiURL(),
  apiTimeout: getApiTimeout(),
  proxyTarget: import.meta.env.VITE_PROXY_TARGET || getBaseURL(),

  // 资源相关配置
  assetsUrl: import.meta.env.VITE_ASSETS_URL || "",
  webURL: import.meta.env.VITE_WEB_URL || "",
  jumpURL: getJumpURL(false), // 默认使用非Vue URL
  jumpVueURL: import.meta.env.VITE_JUMP_URL_VUE || "", // Vue URL

  // 功能开关
  enableMock: import.meta.env.VITE_ENABLE_MOCK === "true",
  enableDevtools: import.meta.env.VITE_ENABLE_DEVTOOLS === "true",
  enableConsole: isConsoleEnabled(),
  enableVConsole: import.meta.env.VITE_ENABLE_VCONSOLE === "true",

  // 构建相关配置
  dropConsole: import.meta.env.VITE_DROP_CONSOLE === "true",
  buildCompress: import.meta.env.VITE_BUILD_COMPRESS || "gzip",
  buildCompressDeleteOriginFile: import.meta.env.VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE === "true",

  // 代理配置
  proxy: (() => {
    try {
      return JSON.parse(import.meta.env.VITE_PROXY || "[]");
    } catch {
      return [];
    }
  })(),

  // 环境判断
  isDev: isDevelopment(),
  isTest: isTest(),
  isStaging: isStaging(),
  isProd: isProduction(),
};

// ==================== 默认导出 ====================

/**
 * 默认导出环境配置对象
 */
export default envConfig;
