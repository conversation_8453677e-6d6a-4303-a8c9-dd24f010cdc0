<template>
  <div class="title-demo">
    <h1>Dynamic Title Demo</h1>
    <p>Current page title: <strong>{{ currentTitle }}</strong></p>
    
    <div class="navigation-buttons">
      <button @click="navigateTo('/')" class="nav-btn">
        Go to Home (Pay Result)
      </button>
      <button @click="navigateTo('/download')" class="nav-btn">
        Go to Download Page
      </button>
      <button @click="navigateTo('/notSupported')" class="nav-btn">
        Go to Not Supported Page
      </button>
    </div>
    
    <div class="title-info">
      <h2>Title Configuration:</h2>
      <ul>
        <li><strong>Pay Result pages</strong> (/, /cocos/payResult, /vueH5/payResult): "Nustar Pay Result"</li>
        <li><strong>Download pages</strong> (/download, /notSupported): "Nustar Download"</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const currentTitle = ref(document.title);

// 监听路由变化，更新当前标题显示
watch(() => route.path, () => {
  // 使用 nextTick 确保标题已经更新
  setTimeout(() => {
    currentTitle.value = document.title;
  }, 100);
}, { immediate: true });

onMounted(() => {
  currentTitle.value = document.title;
});

const navigateTo = (path: string) => {
  router.push(path);
};
</script>

<style scoped>
.title-demo {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.navigation-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.nav-btn {
  padding: 12px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.nav-btn:hover {
  background-color: #0056b3;
}

.title-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 5px;
  margin-top: 20px;
}

.title-info h2 {
  margin-top: 0;
  color: #495057;
}

.title-info ul {
  list-style-type: disc;
  padding-left: 20px;
}

.title-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

p {
  font-size: 18px;
  text-align: center;
  margin-bottom: 20px;
}

strong {
  color: #007bff;
}
</style>
