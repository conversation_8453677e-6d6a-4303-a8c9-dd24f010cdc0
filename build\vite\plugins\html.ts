import type { ImportMetaEnv } from "../../../env.d.ts";

export function configHtmlPlugin(env: ImportMetaEnv) {
  const { VITE_APP_TITLE = "Vue3" } = env;

  return {
    name: "html-transform",
    transformIndexHtml(html: any) {
      // Inject title tag after charset meta tag since we removed the static title
      return html.replace(
        /<meta charset="UTF-8" \/>/,
        `<meta charset="UTF-8" />\n    <title>${VITE_APP_TITLE}</title>`
      );
    },
  };
}
