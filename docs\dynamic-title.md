# Dynamic Page Title Configuration

This document explains how the dynamic page title system works in the Nustar application.

## Overview

The application now supports dynamic page titles that change based on the current route. The titles are configured as follows:

- **Pay Result pages** (`/`, `/cocos/payResult`, `/vueH5/payResult`): "Nustar Pay Result"
- **Download pages** (`/download`, `/notSupported`): "Nustar Download"

## Implementation

### 1. Route Configuration

Each route in `src/router/index.ts` now includes a `meta.title` property:

```typescript
{
  name: "download",
  path: "/download",
  component: () => import("../views/DownloadPage/index.vue"),
  meta: {
    title: "Nustar Download"
  }
}
```

### 2. Router Guard

A `beforeEach` router guard automatically sets the document title when navigating between routes:

```typescript
router.beforeEach((to, _from, next) => {
  if (to.meta?.title) {
    setPageTitle(to.meta.title as string);
  } else {
    const title = getPageTitle(to.name as string);
    setPageTitle(title);
  }
  next();
});
```

### 3. Utility Functions

The `src/utils/env.ts` file provides utility functions for title management:

- `getPageTitle(routeName?: string)`: Returns the appropriate title for a given route name
- `setPageTitle(title: string)`: Sets the document title
- `getAppTitle()`: Returns the default application title from environment variables

### 4. TypeScript Support

Type definitions are provided in `src/types/router.d.ts` to ensure type safety:

```typescript
declare module "vue-router" {
  interface RouteMeta {
    title?: string;
  }
}
```

## Testing

You can test the dynamic title functionality by:

1. Starting the development server: `npm run start:dev`
2. Visiting the demo page: `http://localhost:81/title-demo`
3. Using the navigation buttons to see titles change in real-time

## Adding New Routes

To add a new route with a custom title:

1. Add the route configuration with a `meta.title` property
2. Update the `titleMap` in `getPageTitle()` function if needed
3. The title will automatically be set when navigating to the route

## Environment Variables

The default title fallback is controlled by the `VITE_APP_TITLE` environment variable:

- Development: "Nustar (Development)"
- Test: "Nustar (Test)"
- Staging: "Nustar (Staging)"
- Production: "Nustar"

## HTML Template Changes

The `index.html` file has been cleaned up to remove redundant code:

- **Removed static title tag**: The hardcoded `<title>NuStar</title>` was removed since titles are now managed dynamically
- **Removed duplicate viewport meta**: The static viewport meta tag was removed since it's dynamically generated by the script
- **Updated HTML plugin**: The Vite HTML plugin now injects the title tag dynamically during build

## Browser Support

This implementation uses standard `document.title` API which is supported in all modern browsers.
